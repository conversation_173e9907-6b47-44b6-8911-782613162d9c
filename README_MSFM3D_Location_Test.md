# MSFM3D 三维地震定位测试代码

本项目包含基于MSFM3D算法的三维地震源定位测试代码，用于评估定位精度和分析误差特性。

## 文件说明

### 主要文件

1. **`analyze_msfm3d_location_errors.m`** - 完整的定位误差分析代码
   - 10个测试震源的全面分析
   - 详细的统计分析和可视化
   - 适合深入研究定位性能

2. **`quick_msfm3d_error_test.m`** - 快速测试版本
   - 5个测试震源的简化分析
   - 小尺寸模型，计算速度快
   - 适合快速验证和调试

3. **`test_msfm3d_location.m`** - 单点定位测试
   - 单个震源的详细定位过程
   - 完整的可视化展示
   - 适合理解算法原理

## 使用方法

### 前提条件

1. **MSFM3D MEX函数**：确保`msfm3d.mexw64`（Windows）或相应平台的MEX文件在MATLAB路径中
2. **MATLAB版本**：建议R2018b或更高版本
3. **所需工具箱**：基础MATLAB（无需额外工具箱）

### 快速开始

```matlab
% 1. 快速测试（推荐首次使用）
quick_msfm3d_error_test

% 2. 完整误差分析
analyze_msfm3d_location_errors

% 3. 单点定位演示
test_msfm3d_location
```

## 算法特点

### 3D速度模型
- **分层结构**：模拟真实地质分层
- **横向变化**：包含速度的横向梯度
- **异常体**：低速异常体模拟断层或破碎带
- **速度范围**：1000-8000 m/s

### 台站网络
- **空间分布**：覆盖研究区域的合理布局
- **台站数量**：5-9个台站
- **地表布设**：所有台站位于地表（z=1）

### 定位算法
- **网格搜索**：3D空间的系统搜索
- **走时计算**：基于MSFM3D的高精度走时
- **残差最小化**：RMS走时残差最小化
- **噪声处理**：考虑观测噪声影响

## 结果分析

### 定位精度指标
- **平均误差**：所有测试点的平均定位误差
- **RMS误差**：均方根误差
- **最大/最小误差**：误差范围
- **标准差**：误差分散程度

### 走时残差
- **平均残差**：走时拟合质量
- **残差分布**：各台站的残差特性

### 可视化内容
1. **3D散点图**：震源和台站的三维分布
2. **平面投影**：XY、XZ、YZ平面的投影图
3. **误差分布**：各测试点的误差统计
4. **残差分析**：走时残差的分布特性
5. **速度模型**：不同深度的速度切片

## 参数调整

### 模型参数
```matlab
% 模型尺寸
nx = 100; ny = 100; nz = 50;  % 可调整以平衡精度和速度

% 速度结构
base_velocity = [2000, 3500, 4500, 5500, 6500];  % 各层速度
```

### 搜索参数
```matlab
% 搜索步长
search_step = 3;  % 较小值提高精度，较大值加快速度

% 搜索范围
search_range_x = 15:search_step:85;
search_range_y = 15:search_step:85;
search_range_z = 8:search_step:42;
```

### 噪声水平
```matlab
noise_level = 0.002;  % 2ms标准差，可调整模拟不同观测条件
```

## 性能优化

### 计算速度优化
1. **减小模型尺寸**：降低nx、ny、nz值
2. **增大搜索步长**：提高search_step值
3. **减少台站数量**：使用较少台站
4. **限制搜索范围**：缩小搜索区域

### 精度提升
1. **增加模型分辨率**：提高网格密度
2. **减小搜索步长**：更精细的搜索
3. **增加台站数量**：改善几何覆盖
4. **降低噪声水平**：模拟更好的观测条件

## 典型结果

### 快速测试预期结果
- **平均定位误差**：2-5个网格单位
- **RMS误差**：3-6个网格单位
- **平均走时残差**：1-3毫秒
- **计算时间**：每个台站10-30秒

### 影响因素
1. **震源深度**：深层震源误差通常较大
2. **台站几何**：良好的方位角覆盖减小误差
3. **速度结构**：复杂结构增加定位难度
4. **观测噪声**：噪声水平直接影响精度

## 故障排除

### 常见问题
1. **MEX函数未找到**：确保msfm3d.mexw64在路径中
2. **内存不足**：减小模型尺寸或搜索范围
3. **计算时间过长**：使用quick版本或调整参数
4. **定位误差过大**：检查速度模型和台站布局

### 调试建议
1. 先运行quick版本验证基本功能
2. 检查速度模型的合理性
3. 验证台站坐标的正确性
4. 逐步增加模型复杂度

## 扩展应用

### 实际数据应用
1. 替换为真实速度模型
2. 使用实际台站坐标
3. 输入真实观测走时
4. 调整搜索范围和精度

### 算法改进
1. 实现多尺度搜索
2. 添加P波和S波联合定位
3. 引入权重处理
4. 实现不确定性分析

## 参考文献

- Kroon, D. (2009). Multistencil Fast Marching Method
- 相关地震定位算法文献

## 联系方式

如有问题或建议，请联系开发者。

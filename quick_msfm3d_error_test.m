% MSFM3D 快速定位误差测试
% 简化版本，用于快速验证算法性能

clear; clc; close all;

fprintf('=== MSFM3D 快速定位误差测试 ===\n\n');

%% 1. 创建简化的3D速度模型
fprintf('1. 创建3D速度模型...\n');

% 小尺寸模型以加快计算
nx = 50; ny = 50; nz = 25;
SpeedImage3D = zeros(nx, ny, nz);

% 简单分层模型
for k = 1:nz
    if k <= 5
        SpeedImage3D(:, :, k) = 2000;      % 表层
    elseif k <= 10
        SpeedImage3D(:, :, k) = 3000;      % 第二层
    elseif k <= 15
        SpeedImage3D(:, :, k) = 4000;      % 第三层
    elseif k <= 20
        SpeedImage3D(:, :, k) = 5000;      % 第四层
    else
        SpeedImage3D(:, :, k) = 6000;      % 底层
    end
end

% 添加一个低速异常体
SpeedImage3D(20:25, 20:25, 10:15) = 1500;

fprintf('   模型尺寸: %d × %d × %d\n', nx, ny, nz);

%% 2. 设置台站网络
stations = [5, 5, 1;      % 台站1
           45, 5, 1;     % 台站2
           45, 45, 1;    % 台站3
           5, 45, 1;     % 台站4
           25, 25, 1];   % 台站5 (中心)

num_stations = size(stations, 1);
fprintf('   台站数量: %d\n', num_stations);

%% 3. 设置测试震源
test_sources = [15, 15, 8;    % 浅层
               35, 35, 8;    % 浅层
               25, 25, 12;   % 中层
               20, 30, 18;   % 深层
               30, 20, 18];  % 深层

num_tests = size(test_sources, 1);
fprintf('   测试震源数量: %d\n', num_tests);

%% 4. 计算走时表
fprintf('\n2. 计算走时表...\n');
travel_time_tables = cell(num_stations, 1);

for i = 1:num_stations
    tic;
    fprintf('   台站 %d...', i);
    travel_time_tables{i} = msfm3d(SpeedImage3D, stations(i,:)', true, true);
    fprintf(' 完成 (%.2f秒)\n', toc);
end

%% 5. 执行定位测试
fprintf('\n3. 执行定位测试...\n');

location_errors = zeros(num_tests, 1);
estimated_sources = zeros(num_tests, 3);
residuals = zeros(num_tests, 1);

for test_idx = 1:num_tests
    true_source = test_sources(test_idx, :);
    fprintf('\n测试点 %d: [%d, %d, %d]\n', test_idx, true_source(1), true_source(2), true_source(3));
    
    % 生成观测数据
    observed_times = zeros(num_stations, 1);
    noise_level = 0.001; % 1ms噪声
    
    for i = 1:num_stations
        observed_times(i) = travel_time_tables{i}(true_source(1), true_source(2), true_source(3));
        observed_times(i) = observed_times(i) + noise_level * randn();
    end
    
    % 网格搜索定位
    search_range_x = 8:2:42;
    search_range_y = 8:2:42;
    search_range_z = 5:2:20;
    
    min_residual = inf;
    best_location = [0, 0, 0];
    
    for i = 1:length(search_range_x)
        for j = 1:length(search_range_y)
            for k = 1:length(search_range_z)
                x = search_range_x(i);
                y = search_range_y(j);
                z = search_range_z(k);
                
                % 计算理论走时
                theoretical_times = zeros(num_stations, 1);
                for s = 1:num_stations
                    theoretical_times(s) = travel_time_tables{s}(x, y, z);
                end
                
                % 计算残差
                residual = sqrt(mean((observed_times - theoretical_times).^2));
                
                if residual < min_residual
                    min_residual = residual;
                    best_location = [x, y, z];
                end
            end
        end
    end
    
    % 计算误差
    error = norm(true_source - best_location);
    
    % 存储结果
    location_errors(test_idx) = error;
    estimated_sources(test_idx, :) = best_location;
    residuals(test_idx) = min_residual;
    
    fprintf('定位结果: [%d, %d, %d]\n', best_location(1), best_location(2), best_location(3));
    fprintf('定位误差: %.2f 网格单位\n', error);
    fprintf('走时残差: %.6f 秒\n', min_residual);
end

%% 6. 统计分析
fprintf('\n=== 统计结果 ===\n');
fprintf('平均定位误差: %.2f 网格单位\n', mean(location_errors));
fprintf('误差标准差: %.2f 网格单位\n', std(location_errors));
fprintf('最大误差: %.2f 网格单位\n', max(location_errors));
fprintf('最小误差: %.2f 网格单位\n', min(location_errors));
fprintf('RMS误差: %.2f 网格单位\n', sqrt(mean(location_errors.^2)));

fprintf('\n平均走时残差: %.6f 秒\n', mean(residuals));
fprintf('残差标准差: %.6f 秒\n', std(residuals));

%% 7. 简单可视化
fprintf('\n4. 生成可视化...\n');

figure(1);
set(gcf, 'Position', [100, 100, 1200, 800]);

% 3D散点图
subplot(2, 2, 1);
scatter3(stations(:,1), stations(:,2), stations(:,3), 150, '^k', 'filled');
hold on;

colors = lines(num_tests);
for i = 1:num_tests
    scatter3(test_sources(i,1), test_sources(i,2), test_sources(i,3), ...
        200, colors(i,:), '*', 'LineWidth', 2);
    scatter3(estimated_sources(i,1), estimated_sources(i,2), estimated_sources(i,3), ...
        100, colors(i,:), 'o', 'LineWidth', 2);
    plot3([test_sources(i,1), estimated_sources(i,1)], ...
          [test_sources(i,2), estimated_sources(i,2)], ...
          [test_sources(i,3), estimated_sources(i,3)], ...
          '--', 'Color', colors(i,:), 'LineWidth', 1);
end

xlabel('X'); ylabel('Y'); zlabel('Z');
title('3D定位结果');
legend('台站', '真实震源', '定位结果', 'Location', 'best');
grid on;

% 定位误差
subplot(2, 2, 2);
bar(1:num_tests, location_errors);
title('定位误差');
xlabel('测试点'); ylabel('误差 (网格单位)');
grid on;

% 走时残差
subplot(2, 2, 3);
bar(1:num_tests, residuals*1000); % 转换为毫秒
title('走时残差');
xlabel('测试点'); ylabel('残差 (毫秒)');
grid on;

% XY平面投影
subplot(2, 2, 4);
slice_z = 10;
pcolor(squeeze(SpeedImage3D(:,:,slice_z))); 
shading interp; colormap(jet); colorbar;
hold on;
plot(stations(:,2), stations(:,1), '^k', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
for i = 1:num_tests
    plot(test_sources(i,2), test_sources(i,1), '*', 'Color', colors(i,:), 'MarkerSize', 12);
    plot(estimated_sources(i,2), estimated_sources(i,1), 'o', 'Color', colors(i,:), 'MarkerSize', 8);
end
title(sprintf('XY投影 (Z=%d)', slice_z));
xlabel('Y'); ylabel('X');

fprintf('测试完成！\n');

%% 8. 输出详细结果表格
fprintf('\n=== 详细结果 ===\n');
fprintf('测试点\t真实位置\t\t定位结果\t\t误差\t残差(ms)\n');
fprintf('------\t--------\t\t--------\t\t----\t-------\n');
for i = 1:num_tests
    fprintf('%d\t[%2d,%2d,%2d]\t\t[%2d,%2d,%2d]\t\t%.2f\t%.3f\n', ...
        i, test_sources(i,1), test_sources(i,2), test_sources(i,3), ...
        estimated_sources(i,1), estimated_sources(i,2), estimated_sources(i,3), ...
        location_errors(i), residuals(i)*1000);
end

% MSFM3D 三维地震定位误差分析完整代码
function analyze_msfm3d_location_errors()
clear; clc; close all;

fprintf('=== MSFM3D 三维地震定位误差分析 ===\n\n');

% ===== 1. 设置多个测试震源 =====
test_sources = [
    30, 30, 15;   % 测试点1 - 浅层左下
    70, 30, 15;   % 测试点2 - 浅层右下
    70, 70, 15;   % 测试点3 - 浅层右上
    30, 70, 15;   % 测试点4 - 浅层左上
    50, 50, 15;   % 测试点5 - 浅层中心
    40, 40, 25;   % 测试点6 - 中层
    60, 60, 25;   % 测试点7 - 中层
    45, 55, 35;   % 测试点8 - 深层
    55, 45, 35;   % 测试点9 - 深层
    50, 50, 30    % 测试点10 - 深层中心
];

num_tests = size(test_sources, 1);
fprintf('测试震源数量: %d\n', num_tests);

% 存储结果
location_errors = zeros(num_tests, 1);
estimated_sources = zeros(num_tests, 3);
residuals = zeros(num_tests, 1);
computation_times = zeros(num_tests, 1);

% ===== 2. 建立3D速度模型和台站网络 =====
fprintf('\n1. 构建3D速度模型和台站网络...\n');
[SpeedImage3D, stations] = setup_3d_model();

% ===== 3. 计算走时表 =====
fprintf('\n2. 计算各台站走时表...\n');
travel_time_tables = compute_travel_time_tables(SpeedImage3D, stations);

% ===== 4. 对每个测试震源进行定位 =====
fprintf('\n3. 开始多点定位测试...\n');
for test_idx = 1:num_tests
    true_source = test_sources(test_idx, :);
    fprintf('\n--- 测试震源 %d: [%d, %d, %d] ---\n', test_idx, ...
        true_source(1), true_source(2), true_source(3));

    % 生成观测数据
    observed_times = generate_observed_times_3d(true_source, travel_time_tables);

    % 执行定位
    tic;
    [estimated_source, min_residual] = grid_search_location_3d(observed_times, travel_time_tables);
    computation_times(test_idx) = toc;

    % 计算误差
    error = norm(true_source - estimated_source);

    % 存储结果
    location_errors(test_idx) = error;
    estimated_sources(test_idx, :) = estimated_source;
    residuals(test_idx) = min_residual;

    % 输出单个结果
    fprintf('真实位置: [%d, %d, %d]\n', true_source(1), true_source(2), true_source(3));
    fprintf('定位结果: [%d, %d, %d]\n', estimated_source(1), estimated_source(2), estimated_source(3));
    fprintf('定位误差: %.2f 网格单位\n', error);
    fprintf('走时残差: %.6f 秒\n', min_residual);
    fprintf('计算时间: %.2f 秒\n', computation_times(test_idx));
end

% ===== 5. 统计分析 =====
fprintf('\n=== 误差统计分析 ===\n');
analyze_statistics(location_errors, residuals, computation_times);

% ===== 6. 可视化结果 =====
fprintf('\n4. 生成可视化结果...\n');
visualize_3d_results(SpeedImage3D, stations, test_sources, estimated_sources, location_errors, residuals);

fprintf('\n=== 3D定位误差分析完成 ===\n');
end

% ===== 辅助函数 =====

function [SpeedImage3D, stations] = setup_3d_model()
% 建立3D速度模型和台站网络

% 模型尺寸
nx = 100; ny = 100; nz = 50;
SpeedImage3D = zeros(nx, ny, nz);

% 创建分层速度模型
for k = 1:nz
    depth_ratio = k / nz;
    
    % 基础分层速度结构
    if depth_ratio <= 0.2
        base_velocity = 2000;  % 表层
    elseif depth_ratio <= 0.4
        base_velocity = 3500;  % 第二层
    elseif depth_ratio <= 0.6
        base_velocity = 4500;  % 第三层
    elseif depth_ratio <= 0.8
        base_velocity = 5500;  % 第四层
    else
        base_velocity = 6500;  % 底层
    end
    
    % 添加横向速度变化
    for i = 1:nx
        for j = 1:ny
            lateral_variation = 1 + 0.1 * sin(2*pi*i/nx) * cos(2*pi*j/ny);
            random_variation = 1 + 0.05 * (rand() - 0.5);
            SpeedImage3D(i, j, k) = base_velocity * lateral_variation * random_variation;
        end
    end
end

% 添加低速异常体
SpeedImage3D(30:40, 30:40, 15:25) = 1500;
SpeedImage3D(60:70, 60:70, 20:30) = 1800;
SpeedImage3D(45:55, 20:30, 10:20) = 1600;

% 确保速度值在合理范围内
SpeedImage3D = max(SpeedImage3D, 1000);
SpeedImage3D = min(SpeedImage3D, 8000);

% 设置台站位置
stations = [10, 10, 1;     % 台站1
           90, 10, 1;     % 台站2
           90, 90, 1;     % 台站3
           10, 90, 1;     % 台站4
           50, 50, 1;     % 台站5
           30, 30, 1;     % 台站6
           70, 70, 1;     % 台站7
           30, 70, 1;     % 台站8
           70, 30, 1];    % 台站9

fprintf('   模型尺寸: %d × %d × %d\n', nx, ny, nz);
fprintf('   台站数量: %d\n', size(stations, 1));
fprintf('   速度范围: %.0f - %.0f m/s\n', min(SpeedImage3D(:)), max(SpeedImage3D(:)));
end

function travel_time_tables = compute_travel_time_tables(SpeedImage3D, stations)
% 计算各台站的走时表

num_stations = size(stations, 1);
travel_time_tables = cell(num_stations, 1);

total_time = 0;
for i = 1:num_stations
    tic;
    fprintf('   计算台站 %d 走时表...', i);
    
    travel_time_tables{i} = msfm3d(SpeedImage3D, stations(i,:)', true, true);
    
    elapsed = toc;
    total_time = total_time + elapsed;
    fprintf(' 完成 (%.2f秒)\n', elapsed);
end

fprintf('   总计算时间: %.2f秒\n', total_time);
end

function observed_times = generate_observed_times_3d(true_source, travel_time_tables)
% 生成观测走时数据

num_stations = length(travel_time_tables);
observed_times = zeros(num_stations, 1);
noise_level = 0.002; % 2ms标准差

for i = 1:num_stations
    % 提取理论走时
    observed_times(i) = travel_time_tables{i}(true_source(1), true_source(2), true_source(3));
    % 添加观测噪声
    observed_times(i) = observed_times(i) + noise_level * randn();
end
end

function [best_location, min_residual] = grid_search_location_3d(observed_times, travel_time_tables)
% 3D网格搜索定位

% 搜索参数
search_step = 3;  % 搜索步长
search_range_x = 15:search_step:85;
search_range_y = 15:search_step:85;
search_range_z = 8:search_step:42;

min_residual = inf;
best_location = [0, 0, 0];
num_stations = length(travel_time_tables);

for i = 1:length(search_range_x)
    for j = 1:length(search_range_y)
        for k = 1:length(search_range_z)
            x = search_range_x(i);
            y = search_range_y(j);
            z = search_range_z(k);

            % 计算理论走时
            theoretical_times = zeros(num_stations, 1);
            for s = 1:num_stations
                theoretical_times(s) = travel_time_tables{s}(x, y, z);
            end

            % 计算残差
            residual = sqrt(mean((observed_times - theoretical_times).^2));

            if residual < min_residual
                min_residual = residual;
                best_location = [x, y, z];
            end
        end
    end
end
end

function analyze_statistics(location_errors, residuals, computation_times)
% 统计分析

fprintf('定位误差统计:\n');
fprintf('  平均误差: %.2f 网格单位\n', mean(location_errors));
fprintf('  误差标准差: %.2f 网格单位\n', std(location_errors));
fprintf('  最大误差: %.2f 网格单位\n', max(location_errors));
fprintf('  最小误差: %.2f 网格单位\n', min(location_errors));
fprintf('  RMS误差: %.2f 网格单位\n', sqrt(mean(location_errors.^2)));

fprintf('\n走时残差统计:\n');
fprintf('  平均残差: %.6f 秒\n', mean(residuals));
fprintf('  残差标准差: %.6f 秒\n', std(residuals));
fprintf('  最大残差: %.6f 秒\n', max(residuals));
fprintf('  最小残差: %.6f 秒\n', min(residuals));

fprintf('\n计算时间统计:\n');
fprintf('  平均时间: %.2f 秒\n', mean(computation_times));
fprintf('  总计算时间: %.2f 秒\n', sum(computation_times));
fprintf('  最长时间: %.2f 秒\n', max(computation_times));
fprintf('  最短时间: %.2f 秒\n', min(computation_times));
end

function visualize_3d_results(SpeedImage3D, stations, true_sources, estimated_sources, location_errors, residuals)
% 3D结果可视化

[nx, ny, nz] = size(SpeedImage3D);
num_tests = size(true_sources, 1);

% 图1: 3D散点图显示所有定位结果
figure(1);
set(gcf, 'Position', [100, 100, 1400, 1000]);

% 子图1: 3D散点图
subplot(2, 3, 1);
% 绘制台站
scatter3(stations(:,1), stations(:,2), stations(:,3), 150, '^k', 'filled');
hold on;

% 绘制真实震源和定位结果
colors = lines(num_tests);
for i = 1:num_tests
    % 真实震源
    scatter3(true_sources(i,1), true_sources(i,2), true_sources(i,3), ...
        200, colors(i,:), '*', 'LineWidth', 2);
    % 定位结果
    scatter3(estimated_sources(i,1), estimated_sources(i,2), estimated_sources(i,3), ...
        100, colors(i,:), 'o', 'LineWidth', 2);
    % 连接线显示误差
    plot3([true_sources(i,1), estimated_sources(i,1)], ...
          [true_sources(i,2), estimated_sources(i,2)], ...
          [true_sources(i,3), estimated_sources(i,3)], ...
          '--', 'Color', colors(i,:), 'LineWidth', 1);
end

xlabel('X'); ylabel('Y'); zlabel('Z');
title('3D定位结果对比');
legend('台站', '真实震源', '定位结果', 'Location', 'best');
grid on; axis equal;

% 子图2: XY平面投影
subplot(2, 3, 2);
slice_z = round(nz/3);
pcolor(squeeze(SpeedImage3D(:,:,slice_z)));
shading interp; colormap(jet); colorbar;
hold on;
plot(stations(:,2), stations(:,1), '^k', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
for i = 1:num_tests
    plot(true_sources(i,2), true_sources(i,1), '*', 'Color', colors(i,:), 'MarkerSize', 12);
    plot(estimated_sources(i,2), estimated_sources(i,1), 'o', 'Color', colors(i,:), 'MarkerSize', 8);
end
title(sprintf('XY平面投影 (Z=%d)', slice_z));
xlabel('Y'); ylabel('X');

% 子图3: XZ平面投影
subplot(2, 3, 3);
slice_y = round(ny/2);
pcolor(squeeze(SpeedImage3D(:,slice_y,:))');
shading interp; colormap(jet); colorbar;
hold on;
for i = 1:num_tests
    plot(true_sources(i,1), true_sources(i,3), '*', 'Color', colors(i,:), 'MarkerSize', 12);
    plot(estimated_sources(i,1), estimated_sources(i,3), 'o', 'Color', colors(i,:), 'MarkerSize', 8);
end
title(sprintf('XZ平面投影 (Y=%d)', slice_y));
xlabel('X'); ylabel('Z');

% 子图4: 定位误差分布
subplot(2, 3, 4);
bar(1:num_tests, location_errors, 'FaceColor', [0.3 0.6 0.9]);
title('各测试点的定位误差');
xlabel('测试点编号'); ylabel('误差 (网格单位)');
grid on;
% 添加平均线
hold on;
mean_error = mean(location_errors);
plot([0.5, num_tests+0.5], [mean_error, mean_error], 'r--', 'LineWidth', 2);
text(num_tests*0.7, mean_error*1.1, sprintf('平均: %.2f', mean_error), 'FontSize', 10);

% 子图5: 走时残差分布
subplot(2, 3, 5);
bar(1:num_tests, residuals*1000, 'FaceColor', [0.9 0.6 0.3]); % 转换为毫秒
title('各测试点的走时残差');
xlabel('测试点编号'); ylabel('残差 (毫秒)');
grid on;
% 添加平均线
hold on;
mean_residual = mean(residuals)*1000;
plot([0.5, num_tests+0.5], [mean_residual, mean_residual], 'r--', 'LineWidth', 2);
text(num_tests*0.7, mean_residual*1.1, sprintf('平均: %.2f ms', mean_residual), 'FontSize', 10);

% 子图6: 误差统计直方图
subplot(2, 3, 6);
histogram(location_errors, 'BinWidth', 0.5, 'FaceColor', [0.6 0.9 0.6]);
title('定位误差分布直方图');
xlabel('误差 (网格单位)'); ylabel('频次');
grid on;

% 添加统计信息
mean_error = mean(location_errors);
std_error = std(location_errors);
text(0.6, 0.8, sprintf('平均: %.2f', mean_error), 'Units', 'normalized', 'FontSize', 10);
text(0.6, 0.7, sprintf('标准差: %.2f', std_error), 'Units', 'normalized', 'FontSize', 10);
text(0.6, 0.6, sprintf('RMS: %.2f', sqrt(mean(location_errors.^2))), 'Units', 'normalized', 'FontSize', 10);

% 图2: 详细的误差分析
figure(2);
set(gcf, 'Position', [200, 200, 1200, 800]);

% 误差与深度的关系
subplot(2, 2, 1);
depths = true_sources(:, 3);
scatter(depths, location_errors, 100, 'filled');
xlabel('震源深度'); ylabel('定位误差 (网格单位)');
title('定位误差 vs 震源深度');
grid on;
% 添加趋势线
p = polyfit(depths, location_errors, 1);
hold on;
plot(depths, polyval(p, depths), 'r--', 'LineWidth', 2);

% 误差与台站距离的关系
subplot(2, 2, 2);
station_center = mean(stations(:, 1:2));
distances = zeros(num_tests, 1);
for i = 1:num_tests
    distances(i) = norm(true_sources(i, 1:2) - station_center);
end
scatter(distances, location_errors, 100, 'filled');
xlabel('到台站网络中心距离'); ylabel('定位误差 (网格单位)');
title('定位误差 vs 台站距离');
grid on;

% X、Y、Z方向的误差分量
subplot(2, 2, 3);
error_components = abs(true_sources - estimated_sources);
bar(1:num_tests, error_components);
title('各方向误差分量');
xlabel('测试点编号'); ylabel('误差分量 (网格单位)');
legend('X方向', 'Y方向', 'Z方向', 'Location', 'best');
grid on;

% 累积误差分布
subplot(2, 2, 4);
sorted_errors = sort(location_errors);
cumulative_prob = (1:num_tests) / num_tests * 100;
plot(sorted_errors, cumulative_prob, 'b-', 'LineWidth', 2);
xlabel('定位误差 (网格单位)'); ylabel('累积概率 (%)');
title('定位误差累积分布');
grid on;

% 添加关键百分位数
percentiles = [50, 80, 95];
for i = 1:length(percentiles)
    idx = round(percentiles(i)/100 * num_tests);
    if idx > 0 && idx <= num_tests
        plot(sorted_errors(idx), percentiles(i), 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r');
        text(sorted_errors(idx)*1.1, percentiles(i), sprintf('P%d: %.2f', percentiles(i), sorted_errors(idx)), 'FontSize', 9);
    end
end

fprintf('可视化完成！\n');
end

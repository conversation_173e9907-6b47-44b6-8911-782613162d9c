% MSFM3D 三维地震源定位测试代码
% 基于多模板快速行进方法的3D定位算法测试

clear; clc; close all;

fprintf('=== MSFM3D 三维地震源定位测试 ===\n\n');

%% 1. 建立3D速度模型
fprintf('1. 构建3D速度模型...\n');

% 模型尺寸
nx = 100; ny = 100; nz = 50;
SpeedImage3D = zeros(nx, ny, nz);

% 创建分层速度模型
for k = 1:nz
    depth_ratio = k / nz;
    
    % 基础分层速度结构
    if depth_ratio <= 0.2
        base_velocity = 2000;  % 表层
    elseif depth_ratio <= 0.4
        base_velocity = 3500;  % 第二层
    elseif depth_ratio <= 0.6
        base_velocity = 4500;  % 第三层
    elseif depth_ratio <= 0.8
        base_velocity = 5500;  % 第四层
    else
        base_velocity = 6500;  % 底层
    end
    
    % 添加横向速度变化
    for i = 1:nx
        for j = 1:ny
            % 添加横向梯度和随机扰动
            lateral_variation = 1 + 0.1 * sin(2*pi*i/nx) * cos(2*pi*j/ny);
            random_variation = 1 + 0.05 * (rand() - 0.5);
            
            SpeedImage3D(i, j, k) = base_velocity * lateral_variation * random_variation;
        end
    end
end

% 添加低速异常体（模拟断层或破碎带）
% 异常体1
SpeedImage3D(30:40, 30:40, 15:25) = 1500;
% 异常体2  
SpeedImage3D(60:70, 60:70, 20:30) = 1800;
% 异常体3
SpeedImage3D(45:55, 20:30, 10:20) = 1600;

% 确保速度值在合理范围内
SpeedImage3D = max(SpeedImage3D, 1000);  % 最小速度1000 m/s
SpeedImage3D = min(SpeedImage3D, 8000);  % 最大速度8000 m/s

fprintf('   模型尺寸: %d × %d × %d\n', nx, ny, nz);
fprintf('   速度范围: %.0f - %.0f m/s\n', min(SpeedImage3D(:)), max(SpeedImage3D(:)));

%% 2. 设置台站位置（3D坐标）
fprintf('\n2. 设置台站网络...\n');

stations = [10, 10, 1;     % 台站1 - 左下角
           90, 10, 1;     % 台站2 - 右下角
           90, 90, 1;     % 台站3 - 右上角
           10, 90, 1;     % 台站4 - 左上角
           50, 50, 1;     % 台站5 - 中心
           30, 30, 1;     % 台站6
           70, 70, 1;     % 台站7
           30, 70, 1;     % 台站8
           70, 30, 1];    % 台站9

num_stations = size(stations, 1);
fprintf('   台站数量: %d\n', num_stations);

%% 3. 真实震源位置（用于生成观测数据）
true_source = [45, 55, 20];  % [x, y, z]
fprintf('\n3. 真实震源位置: [%d, %d, %d]\n', true_source(1), true_source(2), true_source(3));

%% 4. 计算各台站的理论走时表
fprintf('\n4. 计算各台站的走时表...\n');
travel_time_tables = cell(num_stations, 1);

total_time = 0;
for i = 1:num_stations
    tic;
    fprintf('   计算台站 %d 走时表...', i);
    
    % 使用MSFM3D计算从台站到所有网格点的走时
    travel_time_tables{i} = msfm3d(SpeedImage3D, stations(i,:)', true, true);
    
    elapsed = toc;
    total_time = total_time + elapsed;
    fprintf(' 完成 (%.2f秒)\n', elapsed);
end

fprintf('   总计算时间: %.2f秒\n', total_time);

%% 5. 生成观测走时数据（模拟真实观测）
fprintf('\n5. 生成观测数据...\n');

observed_times = zeros(num_stations, 1);
noise_level = 0.002;  % 2ms噪声标准差

for i = 1:num_stations
    % 从走时表中提取真实震源位置的走时
    observed_times(i) = travel_time_tables{i}(true_source(1), true_source(2), true_source(3));
    
    % 添加观测噪声
    observed_times(i) = observed_times(i) + noise_level * randn();
end

fprintf('   观测走时数据:\n');
for i = 1:num_stations
    fprintf('   台站 %d: %.4f 秒\n', i, observed_times(i));
end

%% 6. 网格搜索定位
fprintf('\n6. 开始网格搜索定位...\n');

% 搜索范围（可以根据需要调整密度）
search_step = 5;  % 搜索步长
search_range_x = 20:search_step:80;
search_range_y = 20:search_step:80;  
search_range_z = 10:search_step:40;

fprintf('   搜索范围: X[%d:%d:%d], Y[%d:%d:%d], Z[%d:%d:%d]\n', ...
    search_range_x(1), search_step, search_range_x(end), ...
    search_range_y(1), search_step, search_range_y(end), ...
    search_range_z(1), search_step, search_range_z(end));

min_residual = inf;
best_location = [0, 0, 0];
search_count = 0;
total_search_points = length(search_range_x) * length(search_range_y) * length(search_range_z);

fprintf('   总搜索点数: %d\n', total_search_points);

tic;
for i = 1:length(search_range_x)
    for j = 1:length(search_range_y)
        for k = 1:length(search_range_z)
            search_count = search_count + 1;
            
            x = search_range_x(i);
            y = search_range_y(j);
            z = search_range_z(k);
            
            % 计算该位置到各台站的理论走时
            theoretical_times = zeros(num_stations, 1);
            for s = 1:num_stations
                theoretical_times(s) = travel_time_tables{s}(x, y, z);
            end
            
            % 计算走时残差（RMS）
            residual = sqrt(mean((observed_times - theoretical_times).^2));
            
            % 更新最佳位置
            if residual < min_residual
                min_residual = residual;
                best_location = [x, y, z];
            end
            
            % 显示进度
            if mod(search_count, 1000) == 0 || search_count == total_search_points
                fprintf('   进度: %d/%d (%.1f%%)\n', search_count, total_search_points, ...
                    100*search_count/total_search_points);
            end
        end
    end
end

search_time = toc;
fprintf('   网格搜索完成，用时: %.2f秒\n', search_time);

%% 7. 输出定位结果
fprintf('\n=== 定位结果 ===\n');
fprintf('真实震源位置: [%d, %d, %d]\n', true_source(1), true_source(2), true_source(3));
fprintf('定位震源位置: [%d, %d, %d]\n', best_location(1), best_location(2), best_location(3));

% 计算定位误差
location_error = norm(true_source - best_location);
fprintf('定位误差: %.2f 个网格点\n', location_error);
fprintf('最小走时残差: %.6f 秒\n', min_residual);

% 计算各台站的理论走时和残差
fprintf('\n各台站走时对比:\n');
fprintf('台站\t观测走时\t理论走时\t残差\n');
fprintf('----\t--------\t--------\t----\n');

theoretical_final = zeros(num_stations, 1);
for i = 1:num_stations
    theoretical_final(i) = travel_time_tables{i}(best_location(1), best_location(2), best_location(3));
    residual_i = observed_times(i) - theoretical_final(i);
    fprintf('%d\t%.4f\t\t%.4f\t\t%.4f\n', i, observed_times(i), theoretical_final(i), residual_i);
end

%% 8. 可视化结果
fprintf('\n8. 生成可视化图形...\n');

% 图1: 3D速度模型切片显示
figure(1);
subplot(2,2,1);
slice_z = round(nz/2);
pcolor(squeeze(SpeedImage3D(:,:,slice_z))); 
shading interp; colormap(jet); colorbar;
hold on;
plot(stations(:,2), stations(:,1), '^k', 'MarkerSize', 8, 'MarkerFaceColor', 'k');
plot(true_source(2), true_source(1), '*r', 'MarkerSize', 12, 'LineWidth', 2);
plot(best_location(2), best_location(1), 'og', 'MarkerSize', 8, 'LineWidth', 2);
title(sprintf('速度模型 (Z=%d层)', slice_z));
xlabel('Y'); ylabel('X');

subplot(2,2,2);
slice_y = round(ny/2);
pcolor(squeeze(SpeedImage3D(:,slice_y,:))'); 
shading interp; colormap(jet); colorbar;
hold on;
plot(true_source(1), true_source(3), '*r', 'MarkerSize', 12, 'LineWidth', 2);
plot(best_location(1), best_location(3), 'og', 'MarkerSize', 8, 'LineWidth', 2);
title(sprintf('速度模型 (Y=%d切面)', slice_y));
xlabel('X'); ylabel('Z');

subplot(2,2,3);
slice_x = round(nx/2);
pcolor(squeeze(SpeedImage3D(slice_x,:,:))'); 
shading interp; colormap(jet); colorbar;
hold on;
plot(true_source(2), true_source(3), '*r', 'MarkerSize', 12, 'LineWidth', 2);
plot(best_location(2), best_location(3), 'og', 'MarkerSize', 8, 'LineWidth', 2);
title(sprintf('速度模型 (X=%d切面)', slice_x));
xlabel('Y'); ylabel('Z');

subplot(2,2,4);
% 3D散点图显示台站和震源位置
scatter3(stations(:,1), stations(:,2), stations(:,3), 100, '^k', 'filled');
hold on;
scatter3(true_source(1), true_source(2), true_source(3), 200, '*r', 'LineWidth', 3);
scatter3(best_location(1), best_location(2), best_location(3), 150, 'og', 'LineWidth', 2);
xlabel('X'); ylabel('Y'); zlabel('Z');
title('3D台站和震源分布');
legend('台站', '真实震源', '定位结果', 'Location', 'best');
grid on;

% 图2: 走时残差对比
figure(2);
bar(1:num_stations, [observed_times, theoretical_final]);
xlabel('台站编号');
ylabel('走时 (秒)');
title('观测走时 vs 理论走时');
legend('观测走时', '理论走时', 'Location', 'best');
grid on;

fprintf('可视化完成！\n');
fprintf('\n=== 测试完成 ===\n');
